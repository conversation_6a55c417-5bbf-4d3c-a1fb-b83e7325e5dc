{"name": "civil", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "pdfjs-dist": "^3.11.174", "react": "^19.1.0", "react-dom": "^19.1.0", "react-pdf": "^7.7.3", "react-zoom-pan-pinch": "^3.7.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "vite": "^7.0.4", "vitest": "^3.2.4"}}