# Floor Plan PDF Viewer

A React web application for viewing detailed floor plan PDFs with advanced zoom, pan, and navigation capabilities.

## Features

### 📄 PDF Support
- Upload PDF files via drag & drop or file picker
- Multi-page PDF navigation
- High-quality rendering optimized for detailed floor plans

### 🔍 Advanced Viewing
- **Zoom Controls**: Zoom in/out with precise control (10% to 1000%)
- **Pan & Navigate**: Click and drag to move around the document
- **Mouse Wheel Zoom**: Smooth zooming with mouse wheel
- **Fullscreen Mode**: Immersive viewing experience
- **Reset View**: Quickly return to original view

### ⌨️ Keyboard Shortcuts
- `Ctrl/Cmd + +` - Zoom in
- `Ctrl/Cmd + -` - Zoom out
- `Ctrl/Cmd + 0` - Reset zoom
- `←` / `→` - Navigate between pages
- `Ctrl/Cmd + O` - Upload new file
- `Esc` - Reset view
- `F11` - Toggle fullscreen (browser)

### 📱 Mobile Friendly
- Touch gestures for zoom and pan
- Responsive design for all screen sizes
- Optimized performance for mobile devices

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd floor-plan-viewer
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Usage

1. **Upload a PDF**: Drag and drop a PDF file onto the upload area or click to browse
2. **Navigate**: Use the Previous/Next buttons or arrow keys for multi-page PDFs
3. **Zoom**: Use the zoom controls, mouse wheel, or keyboard shortcuts
4. **Pan**: Click and drag to move around the document
5. **Fullscreen**: Click the fullscreen button for immersive viewing
6. **Help**: Click the Help button to see all keyboard shortcuts

## Technical Details

### Built With
- **React 19** - UI framework
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Styling
- **react-pdf** - PDF rendering
- **react-zoom-pan-pinch** - Zoom and pan functionality
- **PDF.js** - PDF processing engine

### Performance Optimizations
- Lazy loading of PDF pages
- Efficient canvas rendering
- Smooth zoom/pan animations
- Memory management for large files

## Browser Support
- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License
MIT License - see LICENSE file for details
