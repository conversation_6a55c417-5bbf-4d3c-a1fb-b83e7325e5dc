@import "tailwindcss";

/* PDF Viewer Styles */
.react-pdf__Page {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    overflow: hidden;
}

.react-pdf__Page__canvas {
    display: block;
    max-width: 100%;
    height: auto;
}

/* Transform Component Styles */
.react-transform-wrapper {
    width: 100%;
    height: 100%;
}

.react-transform-component {
    width: 100%;
    height: 100%;
    cursor: grab;
}

.react-transform-component:active {
    cursor: grabbing;
}

/* Smooth transitions */
* {
    transition-property: transform, scale;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Loading animation */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}