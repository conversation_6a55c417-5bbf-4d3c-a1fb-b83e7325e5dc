import { useEffect } from 'react'

const useKeyboardShortcuts = ({
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onPrevPage,
  onNextPage,
  onUploadNew,
  enabled = true
}) => {
  useEffect(() => {
    if (!enabled) return

    const handleKeyDown = (event) => {
      // Prevent shortcuts when typing in input fields
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return
      }

      const { key, ctrlKey, metaKey, shiftKey } = event
      const isModifierPressed = ctrlKey || metaKey

      switch (key) {
        case '+':
        case '=':
          if (isModifierPressed) {
            event.preventDefault()
            onZoomIn?.()
          }
          break
        
        case '-':
          if (isModifierPressed) {
            event.preventDefault()
            onZoomOut?.()
          }
          break
        
        case '0':
          if (isModifierPressed) {
            event.preventDefault()
            onResetZoom?.()
          }
          break
        
        case 'ArrowLeft':
          if (!isModifierPressed) {
            event.preventDefault()
            onPrevPage?.()
          }
          break
        
        case 'ArrowRight':
          if (!isModifierPressed) {
            event.preventDefault()
            onNextPage?.()
          }
          break
        
        case 'o':
        case 'O':
          if (isModifierPressed) {
            event.preventDefault()
            onUploadNew?.()
          }
          break
        
        case 'Escape':
          event.preventDefault()
          onResetZoom?.()
          break
        
        default:
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [enabled, onZoomIn, onZoomOut, onResetZoom, onPrevPage, onNextPage, onUploadNew])
}

export default useKeyboardShortcuts
