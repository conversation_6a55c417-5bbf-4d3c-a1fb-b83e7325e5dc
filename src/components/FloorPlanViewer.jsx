import { useState, useRef, useCallback, useEffect } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch'
import useKeyboardShortcuts from '../hooks/useKeyboardShortcuts'
import LoadingSpinner from './LoadingSpinner'
import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`

const FloorPlanViewer = () => {
  const [file, setFile] = useState(null)
  const [numPages, setNumPages] = useState(null)
  const [pageNumber, setPageNumber] = useState(1)
  const [scale, setScale] = useState(1)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [showHelp, setShowHelp] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const fileInputRef = useRef(null)
  const transformRef = useRef(null)
  const viewerRef = useRef(null)

  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    setNumPages(numPages)
    setPageNumber(1)
    setLoading(false)
    setError(null)
  }, [])

  const onDocumentLoadError = useCallback((error) => {
    console.error('Error loading PDF:', error)
    setError('Failed to load PDF. Please make sure the file is a valid PDF.')
    setLoading(false)
  }, [])

  const handleFileUpload = useCallback((event) => {
    const uploadedFile = event.target.files[0]
    if (uploadedFile && uploadedFile.type === 'application/pdf') {
      setFile(uploadedFile)
      setLoading(true)
      setError(null)
    } else {
      setError('Please select a valid PDF file.')
    }
  }, [])

  const handleDragOver = useCallback((event) => {
    event.preventDefault()
  }, [])

  const handleDrop = useCallback((event) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile && droppedFile.type === 'application/pdf') {
      setFile(droppedFile)
      setLoading(true)
      setError(null)
    } else {
      setError('Please drop a valid PDF file.')
    }
  }, [])

  const goToPrevPage = () => {
    setPageNumber(prev => Math.max(1, prev - 1))
  }

  const goToNextPage = () => {
    setPageNumber(prev => Math.min(numPages, prev + 1))
  }

  const resetView = () => {
    setScale(1)
    if (transformRef.current) {
      transformRef.current.resetTransform()
    }
  }

  const handleUploadNew = () => {
    setFile(null)
    setError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
      fileInputRef.current.click()
    }
  }

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      viewerRef.current?.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  // Keyboard shortcuts
  useKeyboardShortcuts({
    onZoomIn: () => transformRef.current?.zoomIn(),
    onZoomOut: () => transformRef.current?.zoomOut(),
    onResetZoom: resetView,
    onPrevPage: goToPrevPage,
    onNextPage: goToNextPage,
    onUploadNew: handleUploadNew,
    enabled: !!file && !loading
  })

  return (
    <div className="w-full h-full">
      {/* Upload Section */}
      {!file && (
        <div className="flex flex-col items-center justify-center min-h-[400px] border-2 border-dashed border-gray-300 rounded-lg bg-white">
          <div
            className="w-full h-full flex flex-col items-center justify-center p-8 cursor-pointer hover:bg-gray-50 transition-colors"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <svg className="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            <h3 className="text-xl font-medium text-gray-900 mb-2">Upload Floor Plan PDF</h3>
            <p className="text-gray-500 text-center mb-4">
              Drag and drop your PDF file here, or click to browse
            </p>
            <button className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
              Choose File
            </button>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept=".pdf"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <div className="flex">
            <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <p className="ml-3 text-sm text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <LoadingSpinner message="Loading PDF..." />
      )}

      {/* PDF Viewer */}
      {file && !loading && (
        <div ref={viewerRef} className={`bg-white rounded-lg shadow-lg overflow-hidden ${isFullscreen ? 'fixed inset-0 z-40 rounded-none' : ''}`}>
          {/* Controls */}
          <div className="flex items-center justify-between p-4 border-b bg-gray-50">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => {
                  setFile(null)
                  setError(null)
                  if (fileInputRef.current) {
                    fileInputRef.current.value = ''
                  }
                }}
                className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
              >
                Upload New File
              </button>
              {numPages > 1 && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={goToPrevPage}
                    disabled={pageNumber <= 1}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-blue-700 transition-colors"
                  >
                    Previous
                  </button>
                  <span className="text-sm text-gray-600">
                    Page {pageNumber} of {numPages}
                  </span>
                  <button
                    onClick={goToNextPage}
                    disabled={pageNumber >= numPages}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-blue-700 transition-colors"
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Zoom: {Math.round(scale * 100)}%</span>
              <button
                onClick={resetView}
                className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
              >
                Reset View
              </button>
              <button
                onClick={toggleFullscreen}
                className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                title="Toggle Fullscreen"
              >
                {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
              </button>
              <button
                onClick={() => setShowHelp(true)}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                title="Keyboard Shortcuts"
              >
                Help
              </button>
            </div>
          </div>

          {/* PDF Display with Zoom/Pan */}
          <div className="relative" style={{
            height: isFullscreen ? 'calc(100vh - 80px)' : 'calc(100vh - 300px)',
            minHeight: isFullscreen ? '600px' : '500px'
          }}>
            <TransformWrapper
              initialScale={1}
              minScale={0.1}
              maxScale={10}
              centerOnInit={true}
              wheel={{ step: 0.1 }}
              onTransformed={(ref) => {
                setScale(ref.state.scale)
              }}
            >
              {({ zoomIn, zoomOut, resetTransform, ...rest }) => {
                // Store transform functions in ref for keyboard shortcuts
                transformRef.current = { zoomIn, zoomOut, resetTransform, ...rest }

                return (
                  <>
                    {/* Zoom Controls */}
                    <div className="absolute top-4 right-4 z-10 flex flex-col space-y-2">
                      <button
                        onClick={() => zoomIn()}
                        className="w-10 h-10 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 flex items-center justify-center"
                        title="Zoom In (Ctrl/Cmd + +)"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </button>
                      <button
                        onClick={() => zoomOut()}
                        className="w-10 h-10 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 flex items-center justify-center"
                        title="Zoom Out (Ctrl/Cmd + -)"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
                        </svg>
                      </button>
                      <button
                        onClick={() => resetTransform()}
                        className="w-10 h-10 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 flex items-center justify-center"
                        title="Reset Zoom (Ctrl/Cmd + 0)"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                      </button>
                    </div>

                    <TransformComponent
                      wrapperClass="w-full h-full"
                      contentClass="w-full h-full flex items-center justify-center"
                    >
                      <Document
                        file={file}
                        onLoadSuccess={onDocumentLoadSuccess}
                        onLoadError={onDocumentLoadError}
                        className="flex items-center justify-center"
                      >
                        <Page
                          pageNumber={pageNumber}
                          renderTextLayer={false}
                          renderAnnotationLayer={false}
                          className="shadow-lg"
                          width={Math.min(window.innerWidth * 0.8, 1200)}
                        />
                      </Document>
                    </TransformComponent>
                  </>
                )
              }}
            </TransformWrapper>
          </div>
        </div>
      )}

      {/* Help Modal */}
      {showHelp && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Keyboard Shortcuts</h3>
              <button
                onClick={() => setShowHelp(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Zoom In:</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl/Cmd + +</kbd>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Zoom Out:</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl/Cmd + -</kbd>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Reset Zoom:</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl/Cmd + 0</kbd>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Previous Page:</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">←</kbd>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Next Page:</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">→</kbd>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Upload New:</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl/Cmd + O</kbd>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Reset View:</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Esc</kbd>
              </div>
            </div>
            <div className="mt-6 text-xs text-gray-500">
              <p>• Use mouse wheel to zoom in/out</p>
              <p>• Click and drag to pan around the document</p>
              <p>• Double-click to fit to screen</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FloorPlanViewer
