import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import FloorPlanViewer from '../FloorPlanViewer'

// Mock the PDF.js worker
vi.mock('react-pdf', () => ({
  Document: ({ children, onLoadSuccess }) => {
    // Simulate successful PDF load
    setTimeout(() => onLoadSuccess?.({ numPages: 1 }), 100)
    return <div data-testid="pdf-document">{children}</div>
  },
  Page: ({ pageNumber }) => <div data-testid={`pdf-page-${pageNumber}`}>Page {pageNumber}</div>,
  pdfjs: {
    GlobalWorkerOptions: { workerSrc: '' },
    version: '3.0.0'
  }
}))

// Mock the zoom/pan library
vi.mock('react-zoom-pan-pinch', () => ({
  TransformWrapper: ({ children }) => (
    <div data-testid="transform-wrapper">
      {children({ zoomIn: vi.fn(), zoomOut: vi.fn(), resetTransform: vi.fn() })}
    </div>
  ),
  TransformComponent: ({ children }) => (
    <div data-testid="transform-component">{children}</div>
  )
}))

describe('FloorPlanViewer', () => {
  it('renders upload area when no file is selected', () => {
    render(<FloorPlanViewer />)
    
    expect(screen.getByText('Upload Floor Plan PDF')).toBeInTheDocument()
    expect(screen.getByText('Drag and drop your PDF file here, or click to browse')).toBeInTheDocument()
  })

  it('shows help modal when help button is clicked', async () => {
    render(<FloorPlanViewer />)
    
    // First upload a file to show the controls
    const file = new File(['dummy pdf content'], 'test.pdf', { type: 'application/pdf' })
    const input = screen.getByRole('button', { name: /choose file/i })
    
    // Simulate file upload
    fireEvent.click(input)
    
    // Wait for PDF to load and help button to appear
    await screen.findByText('Help')
    
    const helpButton = screen.getByText('Help')
    fireEvent.click(helpButton)
    
    expect(screen.getByText('Keyboard Shortcuts')).toBeInTheDocument()
    expect(screen.getByText('Zoom In:')).toBeInTheDocument()
  })

  it('handles file upload correctly', () => {
    render(<FloorPlanViewer />)
    
    const file = new File(['dummy pdf content'], 'test.pdf', { type: 'application/pdf' })
    const input = document.querySelector('input[type="file"]')
    
    fireEvent.change(input, { target: { files: [file] } })
    
    // Should show loading state
    expect(screen.getByText('Loading PDF...')).toBeInTheDocument()
  })

  it('shows error for invalid file type', () => {
    render(<FloorPlanViewer />)
    
    const file = new File(['dummy content'], 'test.txt', { type: 'text/plain' })
    const input = document.querySelector('input[type="file"]')
    
    fireEvent.change(input, { target: { files: [file] } })
    
    expect(screen.getByText('Please select a valid PDF file.')).toBeInTheDocument()
  })
})
